import React from 'react';
import ReactDOM<PERSON>lient from 'react-dom/client';
import singleSpaReact from 'single-spa-react';
import App from './App';
import { enableHMR } from './utils/hmr-helper';

const lifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: App,
  errorBoundary(err, info) {
    return (
      <>
        <h1>Oop! 500</h1>
        <pre>{err.stack}</pre>
        <pre>{info.componentStack}</pre>
      </>
    );
  },
});

export const { bootstrap, mount, unmount } = lifecycles;

// Habilita HMR para desenvolvimento
enableHMR();
