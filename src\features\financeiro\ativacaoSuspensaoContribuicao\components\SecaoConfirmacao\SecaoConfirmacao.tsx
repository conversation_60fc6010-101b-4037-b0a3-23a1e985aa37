import React from 'react';
import * as AtivacaoSuspensao from '../../exports';

interface ISecaoConfirmacaoProps {
  contribuicao: AtivacaoSuspensao.IContribuicaoItem | null;
  clienteAceita: boolean;
  onClienteAceitaChange: (aceita: boolean) => void;
  onConfirmar: () => void;
  onVoltar: () => void;
  dadosCertificado?:
    | AtivacaoSuspensao.IRecuperarContribuicoesCertificadoResponse
    | null
    | undefined;
}

export const SecaoConfirmacao: React.FC<ISecaoConfirmacaoProps> = ({
  contribuicao,
  clienteAceita,
  onClienteAceitaChange,
  onConfirmar,
  onVoltar,
  dadosCertificado,
}) => {
  if (!contribuicao) return null;

  return (
    <AtivacaoSuspensao.Grid>
      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Text
          variant="heading-large-700"
          fontColor="brand-primary-05"
          marginBottom="16px"
        >
          Confirmar cancelamento do Cuidado Extra
        </AtivacaoSuspensao.Text>
      </AtivacaoSuspensao.GridItem>

      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Alert
          variant="information-01"
          icon={<AtivacaoSuspensao.IconInfoRound size="medium" />}
        >
          Antes de prosseguir, confira abaixo as informações do plano.
        </AtivacaoSuspensao.Alert>
      </AtivacaoSuspensao.GridItem>

      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Text
          variant="text-large-700"
          fontColor="content-neutral-05"
          marginBottom="16px"
        >
          Dados do certificado
        </AtivacaoSuspensao.Text>

        <AtivacaoSuspensao.Table
          themeTable="default"
          columns={AtivacaoSuspensao.colunasConfirmacaoFactory()}
          data={AtivacaoSuspensao.dadosTabelaCertificadoFactory(
            dadosCertificado,
          )}
          noDataComponent={
            <AtivacaoSuspensao.Text variant="text-standard-400">
              Dados do certificado não disponíveis
            </AtivacaoSuspensao.Text>
          }
        />
      </AtivacaoSuspensao.GridItem>

      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Text
          variant="text-large-700"
          fontColor="content-neutral-05"
          marginBottom="16px"
        >
          Cuidado Extrass
        </AtivacaoSuspensao.Text>

        <AtivacaoSuspensao.Table
          themeTable="default"
          columns={AtivacaoSuspensao.colunasConfirmacaoFactory()}
          data={AtivacaoSuspensao.dadosTabelaCuidadoExtraFactory(contribuicao)}
          noDataComponent={
            <AtivacaoSuspensao.Text variant="text-standard-400">
              Dados do cuidado extra não disponíveis
            </AtivacaoSuspensao.Text>
          }
        />
      </AtivacaoSuspensao.GridItem>

      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Alert
          variant="information-01"
          icon={<AtivacaoSuspensao.IconInfoRound size="medium" />}
        >
          Este valor é destinado a cuidar do cliente ou de quem ele ama. Já
          pensou em como um apoio financeiro pode ser muito importante no caso
          de seu falecimento?
        </AtivacaoSuspensao.Alert>
      </AtivacaoSuspensao.GridItem>

      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Alert
          variant="information-01"
          icon={<AtivacaoSuspensao.IconInfoRound size="medium" />}
        >
          Ao cancelar o Cuidado Extra, o cliente e sua família deixam de estar
          bem cuidados.
        </AtivacaoSuspensao.Alert>
      </AtivacaoSuspensao.GridItem>

      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Alert
          variant="warning-01"
          icon={<AtivacaoSuspensao.IconWarningRound size="medium" />}
        >
          Atenção: Caso o pagamento seja por débito em conta e o cliente possui
          uma parcela ainda em procedimento de cobrança, o valor será debitado
          da sua conta normalmente.
        </AtivacaoSuspensao.Alert>
      </AtivacaoSuspensao.GridItem>

      <AtivacaoSuspensao.GridItem xs="1">
        <label
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            cursor: 'pointer',
          }}
        >
          <AtivacaoSuspensao.Checkbox
            checked={clienteAceita}
            onChange={checked => onClienteAceitaChange(checked === true)}
          />
          <AtivacaoSuspensao.Text variant="text-standard-400">
            Cliente ciente e mesmo assim deseja cancelar o Cuidado Extra
          </AtivacaoSuspensao.Text>
        </label>
      </AtivacaoSuspensao.GridItem>

      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Grid>
          <AtivacaoSuspensao.GridItem xs="auto">
            <AtivacaoSuspensao.Button variant="secondary" onClick={onVoltar}>
              Voltar
            </AtivacaoSuspensao.Button>
          </AtivacaoSuspensao.GridItem>
          <AtivacaoSuspensao.GridItem xs="auto">
            <AtivacaoSuspensao.Button
              variant="primary"
              onClick={onConfirmar}
              disabled={!clienteAceita}
            >
              Suspender cuidado extra
            </AtivacaoSuspensao.Button>
          </AtivacaoSuspensao.GridItem>
        </AtivacaoSuspensao.Grid>
      </AtivacaoSuspensao.GridItem>
    </AtivacaoSuspensao.Grid>
  );
};
