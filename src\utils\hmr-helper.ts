/**
 * Helper para melhorar o Hot Module Replacement em Single-SPA
 */
import React from 'react';

declare global {
  interface NodeModule {
    hot?: {
      accept(path?: string, callback?: () => void): void;
      dispose(callback: () => void): void;
      data?: any;
    };
  }

  namespace NodeJS {
    interface Module {
      hot?: {
        accept(path?: string, callback?: () => void): void;
        dispose(callback: () => void): void;
        data?: any;
      };
    }
  }
}

export function enableHMR() {
  if (process.env.NODE_ENV === 'development' && module.hot) {
    // Aceita atualizações do módulo atual
    module.hot.accept();

    // Limpa dados antigos quando o módulo é recarregado
    module.hot.dispose(() => {
      // Limpar listeners, timers, etc.
      console.log('🔄 HMR: Limpando módulo...');
    });

    return true;
  }
  return false;
}

export function isHMREnabled(): boolean {
  return process.env.NODE_ENV === 'development' && !!module.hot;
}

/**
 * Wrapper para componentes React que preserva estado durante HMR
 */
export function withHMR<T extends React.ComponentType<any>>(
  Component: T,
  displayName?: string,
): T {
  if (!isHMREnabled()) {
    return Component;
  }

  const WrappedComponent = (props: any) => {
    return React.createElement(Component, props);
  };

  WrappedComponent.displayName =
    displayName || Component.displayName || Component.name;

  return WrappedComponent as T;
}
